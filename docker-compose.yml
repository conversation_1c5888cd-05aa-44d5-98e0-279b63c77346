version: '3.8'

services:
  # Spring Boot应用
  sparrow-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: sparrow-web-api
    ports:
      - "8080:8080"
    environment:
      # JVM参数
      - JAVA_OPTS=-Xms512m -Xmx1024m -XX:+UseG1GC
      # 数据库配置 - 使用Docker内的MySQL
      - SPRING_DATASOURCE_MAIN_CONFIG=shardingsphere-docker.yml
      - SPRING_DATASOURCE_QUARTZ_JDBC_URL=*****************************************************************************************************************************************************************
      - SPRING_DATASOURCE_QUARTZ_USERNAME=root
      - SPRING_DATASOURCE_QUARTZ_PASSWORD=123456
      # Redis配置
      - SPRING_REDIS_HOST=redis
      - SPRING_REDIS_PORT=6379
      - SPRING_REDIS_PASSWORD=
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
      - ./shardingsphere-docker.yml:/app/shardingsphere-docker.yml
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - sparrow-network
    restart: unless-stopped

  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: sparrow-mysql
    environment:
      - MYSQL_ROOT_PASSWORD=123456
      - MYSQL_DATABASE=sparrow_webapi
      - MYSQL_CHARACTER_SET_SERVER=utf8mb4
      - MYSQL_COLLATION_SERVER=utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p123456"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    networks:
      - sparrow-network
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: sparrow-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - sparrow-network
    restart: unless-stopped

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  sparrow-network:
    driver: bridge
