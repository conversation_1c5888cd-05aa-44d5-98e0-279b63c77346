@echo off
echo Starting Sparrow Web API with Docker...

REM 检查Docker是否运行
docker version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Docker is not running. Please start Docker Desktop first.
    pause
    exit /b 1
)

REM 检查jar文件是否存在
if not exist "api\target\sparrow-web-api-1.0.0.RELEASE.jar" (
    echo Error: JAR file not found at api\target\sparrow-web-api-1.0.0.RELEASE.jar
    echo Please build the project first: mvn clean package
    pause
    exit /b 1
)

REM 创建必要的目录
if not exist "logs" mkdir logs
if not exist "data" mkdir data

REM 构建并启动服务
echo Building and starting services...
docker-compose up --build -d

REM 等待服务启动
echo Waiting for services to start...
timeout /t 30 /nobreak >nul

REM 显示服务状态
echo.
echo Service Status:
docker-compose ps

echo.
echo Services are starting up...
echo Web API will be available at: http://localhost:8080
echo MySQL will be available at: localhost:3306 (root/123456)
echo Redis will be available at: localhost:6379
echo.
echo To view logs: docker-compose logs -f sparrow-api
echo To stop services: docker-compose down
echo.
pause
