@echo off
echo Starting Sparrow Web API...

REM 检查jar文件是否存在
if not exist "api\target\sparrow-web-api-1.0.0.RELEASE.jar" (
    echo Error: JAR file not found. Please build the project first: mvn clean package
    pause
    exit /b 1
)

REM 启动服务
echo Building and starting...
docker-compose up --build -d

echo.
echo Service started successfully!
echo Web API is available at: http://localhost:8080
echo.
echo To view logs: docker-compose logs -f
echo To stop service: docker-compose down
echo.
pause
