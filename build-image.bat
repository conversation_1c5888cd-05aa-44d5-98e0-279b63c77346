@echo off
echo Building Sparrow Web API Docker Image...

REM 检查Docker是否运行
docker version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Docker is not running. Please start Docker Desktop first.
    pause
    exit /b 1
)

REM 检查jar文件是否存在
if not exist "api\target\sparrow-web-api-1.0.0.RELEASE.jar" (
    echo Error: JAR file not found. Please build the project first: mvn clean package
    pause
    exit /b 1
)

REM 构建Docker镜像
echo Building Docker image...
docker build -t sparrow-web-api:latest .

if %errorlevel% equ 0 (
    echo.
    echo Docker image built successfully!
    echo Image name: sparrow-web-api:latest
    echo.
    echo To run the container:
    echo docker run -d -p 8080:8080 --name sparrow-api sparrow-web-api:latest
    echo.
    echo To run on different port (e.g., 9090):
    echo docker run -d -p 9090:8080 --name sparrow-api sparrow-web-api:latest
    echo.
) else (
    echo Failed to build Docker image!
)

pause
