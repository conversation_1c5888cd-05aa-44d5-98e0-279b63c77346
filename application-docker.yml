# Docker环境专用配置
spring:
  datasource:
    main:
      config: shardingsphere-docker.yml
    quartz:
      jdbc-url: *****************************************************************************************************************************************************************
      username: root
      password: 123456
  
  redis:
    host: redis
    port: 6379
    password:

# 日志配置 - Docker环境使用控制台输出
logging:
  level:
    root: INFO
    com.chinamobile: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /app/logs/application.log
    max-size: 100MB
    max-history: 30

# 跨域配置 - 允许Docker网络访问
web:
  cors:
    allowed-origins:
      - http://localhost
      - http://localhost:3000
      - http://localhost:8080
      - http://127.0.0.1
      - http://127.0.0.1:3000
      - http://127.0.0.1:8080
