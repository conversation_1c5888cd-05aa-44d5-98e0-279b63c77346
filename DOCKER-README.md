# Sparrow Web API Docker 部署指南

## 概述

本项目已配置为支持Docker容器化部署，包含以下服务：
- **sparrow-api**: Spring Boot Web API应用
- **mysql**: MySQL 8.0 数据库
- **redis**: Redis 7 缓存服务

## 前置要求

1. 安装 [Docker Desktop](https://www.docker.com/products/docker-desktop/)
2. 确保Docker Desktop正在运行
3. 确保以下端口未被占用：
   - 8080 (Web API)
   - 3306 (MySQL)
   - 6379 (Redis)

## 快速启动

### 方式一：使用批处理脚本（推荐）

```bash
# 启动所有服务
./docker-start.bat

# 停止所有服务
./docker-stop.bat
```

### 方式二：使用Docker Compose命令

```bash
# 构建并启动所有服务
docker-compose up --build -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止所有服务
docker-compose down
```

## 服务访问

启动成功后，可以通过以下地址访问服务：

- **Web API**: http://localhost:8080
- **MySQL**: localhost:3306
  - 用户名: root
  - 密码: 123456
  - 数据库: sparrow_webapi
- **Redis**: localhost:6379

## 目录结构

```
├── docker/                 # Docker相关配置
│   ├── mysql/
│   │   └── init/           # MySQL初始化脚本
│   └── redis/
│       └── redis.conf      # Redis配置文件
├── logs/                   # 应用日志目录
├── data/                   # 数据存储目录
├── Dockerfile              # 应用镜像构建文件
├── docker-compose.yml      # 服务编排文件
├── shardingsphere-docker.yml # Docker环境的分片配置
└── docker-start.bat        # 启动脚本
```

## 配置说明

### 环境变量

可以通过修改 `docker-compose.yml` 中的环境变量来调整配置：

```yaml
environment:
  # JVM参数
  - JAVA_OPTS=-Xms512m -Xmx1024m -XX:+UseG1GC
  # 数据库配置
  - SPRING_DATASOURCE_QUARTZ_USERNAME=root
  - SPRING_DATASOURCE_QUARTZ_PASSWORD=123456
  # Redis配置
  - SPRING_REDIS_HOST=redis
```

### 数据持久化

- MySQL数据存储在Docker volume `mysql_data` 中
- Redis数据存储在Docker volume `redis_data` 中
- 应用日志映射到本地 `./logs` 目录

## 常用命令

```bash
# 查看运行中的容器
docker-compose ps

# 查看特定服务的日志
docker-compose logs -f sparrow-api
docker-compose logs -f mysql
docker-compose logs -f redis

# 进入容器
docker-compose exec sparrow-api bash
docker-compose exec mysql mysql -uroot -p123456
docker-compose exec redis redis-cli

# 重启特定服务
docker-compose restart sparrow-api

# 重新构建镜像
docker-compose build --no-cache sparrow-api

# 清理所有数据（谨慎使用）
docker-compose down -v
```

## 故障排除

### 1. 端口冲突
如果遇到端口冲突，可以修改 `docker-compose.yml` 中的端口映射：
```yaml
ports:
  - "8081:8080"  # 将本地端口改为8081
```

### 2. 内存不足
如果应用启动失败，可以调整JVM内存参数：
```yaml
environment:
  - JAVA_OPTS=-Xms256m -Xmx512m
```

### 3. 数据库连接失败
确保MySQL服务完全启动后再启动应用服务。可以使用以下命令检查：
```bash
docker-compose logs mysql
```

### 4. 查看应用健康状态
```bash
# 检查健康状态
docker-compose exec sparrow-api curl http://localhost:8080/actuator/health
```

## 生产环境部署建议

1. **安全性**：
   - 修改默认密码
   - 使用环境变量管理敏感信息
   - 配置防火墙规则

2. **性能优化**：
   - 根据服务器配置调整JVM参数
   - 配置数据库连接池大小
   - 启用Redis持久化

3. **监控**：
   - 配置日志收集
   - 添加应用监控
   - 设置告警机制

4. **备份**：
   - 定期备份数据库
   - 备份应用配置文件
