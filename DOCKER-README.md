# Sparrow Web API Docker 部署指南

## 快速启动

### 前置要求
1. 确保已安装 Docker Desktop 并正在运行
2. 确保项目已构建完成，jar文件位于：`api/target/sparrow-web-api-1.0.0.RELEASE.jar`

### 启动服务

**方式一：使用批处理脚本（推荐）**
```bash
# 启动所有服务
./docker-start.bat

# 停止所有服务  
./docker-stop.bat
```

**方式二：使用Docker Compose命令**
```bash
# 构建并启动
docker-compose up --build -d

# 查看状态
docker-compose ps

# 查看日志
docker-compose logs -f sparrow-api

# 停止服务
docker-compose down
```

## 服务访问

- **Web API**: http://localhost:8080
- **MySQL**: localhost:3306 (用户名: root, 密码: 123456)
- **Redis**: localhost:6379

## 文件说明

- `Dockerfile` - 应用镜像构建文件
- `docker-compose.yml` - 服务编排文件
- `shardingsphere-docker.yml` - Docker环境数据库配置
- `docker-start.bat` - 启动脚本
- `docker-stop.bat` - 停止脚本

## 常用命令

```bash
# 查看应用日志
docker-compose logs -f sparrow-api

# 进入应用容器
docker-compose exec sparrow-api bash

# 重启应用
docker-compose restart sparrow-api

# 查看数据库
docker-compose exec mysql mysql -uroot -p123456

# 查看Redis
docker-compose exec redis redis-cli
```

## 注意事项

1. 首次启动需要等待MySQL初始化完成
2. 应用日志保存在 `./logs` 目录
3. 如需修改端口，请编辑 `docker-compose.yml` 文件
