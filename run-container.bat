@echo off
echo Starting Sparrow Web API Container...

REM 检查Docker是否运行
docker version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Docker is not running. Please start Docker Desktop first.
    pause
    exit /b 1
)

REM 停止并删除已存在的容器
docker stop sparrow-api 2>nul
docker rm sparrow-api 2>nul

REM 运行新容器
echo Running container on port 8080...
docker run -d -p 8080:8080 --name sparrow-api sparrow-web-api:latest

if %errorlevel% equ 0 (
    echo.
    echo Container started successfully!
    echo Web API is available at: http://localhost:8080
    echo.
    echo To view logs: docker logs -f sparrow-api
    echo To stop container: docker stop sparrow-api
    echo.
) else (
    echo Failed to start container!
    echo Please make sure the Docker image exists: docker images
)

pause
